'use client';

import { useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useChat } from '@/contexts/ChatContext';
import { Message, ChatResponsePart } from '@/types';
import { toast } from 'sonner';

export function useCareCanvasActions() {
  const router = useRouter();
  const pathname = usePathname();
  const { currentThreadId, currentThread, createThread, addMessage, isSending, setIsSending } = useChat();

  const isOnJourneysPage = pathname === '/journeys';

  // Function to format messages for API (similar to ChatView)
  const formatMessagesForApi = (messages: Message[]) => {
    const formatted = [];
    for (const msg of messages) {
      if (msg.sender === 'user') {
        formatted.push({ role: 'user', content: msg.content as string });
      } else if (msg.sender === 'ai') {
        const parts = msg.content as ChatResponsePart[];
        for (const part of parts) {
          if (part.type === 'text') {
            formatted.push({ role: 'assistant', content: part.content });
          }
        }
      }
    }
    return formatted;
  };

  // Function to send message to AI (similar to ChatView)
  const sendMessageToAI = async (messages: { role: string; content: any }[]) => {
    setIsSending(true);
    try {
      const res = await fetch('/api/gemini-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages,
          transcripts: [], // No transcripts for refill requests
        }),
      });

      if (!res.ok) throw new Error('AI failed to respond');
      const data: ChatResponsePart[] = await res.json();
      addMessage(data, 'ai');
    } catch (e: any) {
      toast.error(e.message || 'Failed to get AI response');
    } finally {
      setIsSending(false);
    }
  };

  // Generic function to handle chat requests
  const handleChatRequest = useCallback(async (message: string, threadName: string) => {
    if (isOnJourneysPage) {
      // If on journeys page, add to current thread or create new one
      if (currentThreadId && currentThread) {
        // Add message to existing thread and submit to AI
        addMessage(message, 'user');

        // Prepare messages for AI including the new message
        const messagesForApi = formatMessagesForApi(currentThread.messages);
        messagesForApi.push({ role: 'user', content: message });

        // Submit to AI
        await sendMessageToAI(messagesForApi);
      } else {
        // Create new thread with the request
        createThread({
          name: threadName,
          initialMessage: message
        });
      }
    } else {
      // If on other pages, navigate to journeys and create new thread
      const threadId = createThread({
        name: threadName,
        initialMessage: message
      });
      router.push('/journeys');
    }
  }, [
    isOnJourneysPage,
    currentThreadId,
    currentThread,
    addMessage,
    createThread,
    router,
    sendMessageToAI
  ]);

  const handleRefillRequest = useCallback(async (medicationName: string) => {
    const refillMessage = `I'd like to get a refill on ${medicationName}`;
    const threadName = `Refill Request - ${medicationName}`;
    await handleChatRequest(refillMessage, threadName);
  }, [handleChatRequest]);

  const handleAppointmentRequest = useCallback(async (doctorName: string, appointmentType?: 'in-person' | 'telehealth') => {
    if (appointmentType) {
      const appointmentMessage = `I'd like to schedule ${appointmentType === 'in-person' ? 'an in-person' : 'a telehealth'} appointment with ${doctorName}`;
      const threadName = `${appointmentType === 'in-person' ? 'In-Person' : 'Telehealth'} Appointment - ${doctorName}`;
      await handleChatRequest(appointmentMessage, threadName);
    } else {
      // Generic appointment request - let AI ask for preference
      const appointmentMessage = `I'd like to schedule an appointment with ${doctorName}`;
      const threadName = `Appointment - ${doctorName}`;
      await handleChatRequest(appointmentMessage, threadName);
    }
  }, [handleChatRequest]);

  const handleClaimInquiry = useCallback(async (provider: string, date: string) => {
    const claimMessage = `I have a question about this claim from ${provider} on ${date}`;
    const threadName = `Claim Inquiry - ${provider}`;
    await handleChatRequest(claimMessage, threadName);
  }, [handleChatRequest]);

  const handleFindCare = useCallback(async () => {
    const findCareMessage = "Can you help me find care?";
    const threadName = "Find Care";
    await handleChatRequest(findCareMessage, threadName);
  }, [handleChatRequest]);

  const handleDedicatedNurseLine = useCallback(async () => {
    const nurseMessage = "I'd like to schedule a call back with dedicated nurse line";
    const threadName = "Dedicated Nurse Line";
    await handleChatRequest(nurseMessage, threadName);
  }, [handleChatRequest]);

  const handleConditionTelehealth = useCallback(async () => {
    const telehealthMessage = "I'd like to setup a telehealth appointment with a specialist for my condition";
    const threadName = "Condition Telehealth";
    await handleChatRequest(telehealthMessage, threadName);
  }, [handleChatRequest]);

  const handleTelehealthJoin = useCallback(async () => {
    const joinMessage = "Member has joined the telehealth appointment. ...Fast Foward";
    const threadName = "Telehealth Session";
    await handleChatRequest(joinMessage, threadName);
  }, [handleChatRequest]);

  const handleMedicationQuestion = useCallback(async () => {
    const medicationMessage = "I have a question about my current medications";
    const threadName = "Medication Question";
    await handleChatRequest(medicationMessage, threadName);
  }, [handleChatRequest]);

  const handleMedicationReminders = useCallback(async () => {
    const reminderMessage = "I'd like to setup medication reminders";
    const threadName = "Medication Reminders";
    await handleChatRequest(reminderMessage, threadName);
  }, [handleChatRequest]);

  const handleNurselineReschedule = useCallback(async () => {
    const rescheduleMessage = "I'd like to reschedule my nurse line call";
    const threadName = "Reschedule Nurse Line";
    await handleChatRequest(rescheduleMessage, threadName);
  }, [handleChatRequest]);

  // Functions that match ChatView messages for consistency
  const handleScheduleCallBack = useCallback(async () => {
    const nurseMessage = "Schedule a nurse call";
    const threadName = "Schedule Nurse Call";
    await handleChatRequest(nurseMessage, threadName);
  }, [handleChatRequest]);

  const handleScheduleTelehealth = useCallback(async () => {
    const telehealthMessage = "I'd like to schedule a Telehealth appointment with a specialist";
    const threadName = "Schedule Telehealth";
    await handleChatRequest(telehealthMessage, threadName);
  }, [handleChatRequest]);

  const handleMedicationHelp = useCallback(async () => {
    const medicationMessage = "What are some ways you can help me manage my medications?";
    const threadName = "Medication Help";
    await handleChatRequest(medicationMessage, threadName);
  }, [handleChatRequest]);

  const getActiveThreadInfo = useCallback(() => {
    return {
      hasActiveThread: !!currentThreadId && !!currentThread,
      currentThreadId,
      currentThreadName: currentThread?.name,
      isOnJourneysPage
    };
  }, [currentThreadId, currentThread, isOnJourneysPage]);

  return {
    handleRefillRequest,
    handleAppointmentRequest,
    handleClaimInquiry,
    handleFindCare,
    handleDedicatedNurseLine,
    handleConditionTelehealth,
    handleTelehealthJoin,
    handleMedicationQuestion,
    handleMedicationReminders,
    handleNurselineReschedule,
    // ChatView-compatible functions
    handleScheduleCallBack,
    handleScheduleTelehealth,
    handleMedicationHelp,
    getActiveThreadInfo,
    isOnJourneysPage,
    isSending
  };
}
