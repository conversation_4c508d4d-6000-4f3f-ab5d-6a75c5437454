'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  Pill,
  RefreshCw,
  User,
  Calendar,
  AlertCircle,
  Stethoscope,
  Phone,
  Focus,
  FileText,
  HeartHandshake,
  Activity,
  Shield,
  Video,
  Navigation,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useProfile } from '@/hooks/use-profile';
import { useCareCanvasActions } from '@/hooks/use-care-canvas-actions';
import { useFocusAppointments } from '@/hooks/use-focus-appointments';
import { Prescription, CareTeamMember, Claim } from '@/types';
import { cn } from '@/lib/utils';

interface CareCanvasProps {
  className?: string;
  showHeader?: boolean;
}

export function CareCanvas({ className, showHeader = true }: CareCanvasProps) {
  const { profile, isLoading, error } = useProfile();
  const {
    handleRefillRequest,
    handleAppointmentRequest,
    handleClaimInquiry,
    handleFindCare,
    handleDedicatedNurseLine,
    handleConditionTelehealth,
    handleTelehealthJoin,
    handleMedicationQuestion,
    handleMedicationReminders,
    handleNurselineReschedule,
    // ChatView-compatible functions
    handleScheduleCallBack,
    handleScheduleTelehealth,
    handleMedicationHelp
  } = useCareCanvasActions();
  const { appointments } = useFocusAppointments();

  // Collapsible states
  const [isFocusOpen, setIsFocusOpen] = useState(false);
  const [isCareTeamOpen, setIsCareTeamOpen] = useState(false);
  const [isRxOpen, setIsRxOpen] = useState(false);
  const [isClaimsOpen, setIsClaimsOpen] = useState(false);
  const [isConditionMgmtOpen, setIsConditionMgmtOpen] = useState(false);
  const [isPlanUsageOpen, setIsPlanUsageOpen] = useState(false);
  const [isWellnessOpen, setIsWellnessOpen] = useState(false);

  if (isLoading) {
    return (
      <div className={cn("p-4", className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className={cn("p-4", className)}>
        <div className="text-center text-red-500">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p>Failed to load profile data</p>
        </div>
      </div>
    );
  }

  // Filter out supplements and Ethan's prescriptions, get only prescriptions with refill data
  const prescriptions = profile.prescriptions.filter((prescription: Prescription) => {
    const isNotSupplement = !prescription.medicationName.toLowerCase().includes('supplement') &&
                           !prescription.medicationName.toLowerCase().includes('vitamin') &&
                           !prescription.medicationName.toLowerCase().includes('calcium');
    const isNotEthan = prescription.memberName !== 'Ethan Chen';
    return isNotSupplement && isNotEthan;
  });

  // Check if prescription is eligible for refill
  const isEligibleForRefill = (prescription: Prescription): boolean => {
    return (prescription.refillsRemaining ?? 0) > 0 &&
           prescription.lastFillDate !== undefined;
  };

  const handleRefillClick = (prescription: Prescription) => {
    const medicationName = prescription.brandName || prescription.medicationName;
    handleRefillRequest(medicationName);
  };

  // Get recent claims (limit to 3 for display)
  const recentClaims = profile.claims?.slice(0, 3) || [];

  return (
    <div className={cn("space-y-4 pb-26", className)}>
      {showHeader && (
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Care Canvas
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Manage Your Care With Ease
          </p>
        </div>
      )}

      {/* Focus Section */}
      <Card>
        <Collapsible open={isFocusOpen} onOpenChange={setIsFocusOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Focus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span>Focus</span>
                  <Badge variant="secondary" className="ml-2">
                    {appointments.length}
                  </Badge>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isFocusOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              {appointments.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No items to focus on right now
                </p>
              ) : (
                <div className="space-y-3">
                  {appointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="p-3 rounded-lg bg-blue-50 dark:bg-slate-800/80 border dark:border-blue-500/20 space-y-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-sm">
                            {appointment.type === 'telehealth' ? 'Telehealth' : 
                             appointment.type === 'nurseline' ? 'Nurse Line Call' : 'In-Person'} Appointment
                          </h3>
                          <p className="text-xs text-gray-600 dark:text-slate-300">
                            {appointment.provider} • {appointment.specialty}
                          </p>
                          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-slate-400">
                            <Calendar className="h-3 w-3" />
                            <span>{appointment.date} at {appointment.time}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-slate-400">
                            <DollarSign className="h-3 w-3" />
                            <span>Cost: Free of charge</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 pt-2">
                        {appointment.type === 'telehealth' ? (
                          <Button
                            size="sm"
                            onClick={() => handleTelehealthJoin()}
                            className="flex-1 text-xs bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400"
                          >
                            <Video className="h-3 w-3 mr-1" />
                            Join Now
                          </Button>
                        ) : appointment.type === 'nurseline' ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleNurselineReschedule()}
                            className="flex-1 text-xs border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
                          >
                            <Phone className="h-3 w-3 mr-1" />
                            Reschedule
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {/* Placeholder */}}
                            className="flex-1 text-xs border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
                          >
                            <Navigation className="h-3 w-3 mr-1" />
                            Directions
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
      {/* Care Team Section */}
      <Card>
        <Collapsible open={isCareTeamOpen} onOpenChange={setIsCareTeamOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Stethoscope className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  <span>Care Team</span>
                  <Badge variant="secondary" className="ml-2">
                    {profile?.careTeam?.length || 0}
                  </Badge>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isCareTeamOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              {!profile?.careTeam || profile.careTeam.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No care team members found
                </p>
              ) : (
                <div className="space-y-3">
                  {profile.careTeam.map((member) => (
                    <div
                      key={member.providerId}
                      className="p-3 rounded-lg bg-gray-50 dark:bg-slate-800/80 border dark:border-emerald-500/20 space-y-3"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-sm flex flex-col items-start gap-2 sm:items-top sm:flex-row">
                              {member.name}
                              {member.isPrimary && (
                                <Badge variant="outline" className="text-xs border-emerald-300 dark:border-emerald-500 text-emerald-700 dark:text-emerald-300">
                                  PCP
                                </Badge>
                              )}
                            </h3>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-slate-300">
                            {member.specialty}
                          </p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAppointmentRequest(member.name)}
                          className="ml-2 text-xs border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
                        >
                          Schedule
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-slate-700 space-y-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {/* Placeholder */}}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      View Full Care Team
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFindCare()}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      Find Care
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* RX Section */}
      <Card>
        <Collapsible open={isRxOpen} onOpenChange={setIsRxOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Pill className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  <span>Prescriptions</span>
                  <Badge variant="secondary" className="ml-2">
                    {prescriptions.length}
                  </Badge>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isRxOpen ? "rotate-180" : ""
                )} /> 
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              {prescriptions.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No prescriptions found
                </p>
              ) : (
                <div className="space-y-3">
                  {prescriptions.map((prescription) => (
                    <div
                      key={prescription.prescriptionId}
                      className="p-3 rounded-lg bg-gray-50 dark:bg-slate-800/80 border dark:border-indigo-500/20 space-y-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <User className="h-3 w-3 text-gray-500 dark:text-slate-400" />
                            <span className="text-xs text-gray-500 dark:text-slate-400">{prescription.memberName}</span>
                          </div>
                          <h3 className="font-medium text-sm">
                            {prescription.medicationName}
                            {prescription.brandName && (
                              <span className="text-gray-600 dark:text-slate-300 ml-1">
                                ({prescription.brandName})
                              </span>
                            )}
                          </h3>
                          <p className="text-xs text-gray-600 dark:text-slate-300">
                            {prescription.frequency}
                          </p>
                          {prescription.refillsRemaining !== undefined && (
                            <p className="text-xs text-gray-500 dark:text-slate-400">
                              {prescription.refillsRemaining} refills left
                            </p>
                          )}
                        </div>

                        {isEligibleForRefill(prescription) && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRefillClick(prescription)}
                            className="ml-2 flex items-center gap-1 text-xs"
                          >
                            <RefreshCw className="h-3 w-3" />
                            Refill
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-slate-700 space-y-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {/* Placeholder */}}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      View All Prescriptions
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMedicationQuestion()}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      Ask about my medications
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMedicationReminders()}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      Setup Medication Reminders
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Plan Usage Section */}
      <Card>
        <Collapsible open={isPlanUsageOpen} onOpenChange={setIsPlanUsageOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  <span>Plan Usage</span>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isPlanUsageOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-8">
              <div className="space-y-4">
                {/* Deductible Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Deductible (Individual)</span>
                    <span className="text-sm text-gray-600 dark:text-slate-300">
                      ${profile?.planUsage?.deductible?.individual?.met?.toFixed(2) || '0.00'} / ${profile?.planUsage?.deductible?.individual?.amount?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                  <Progress
                    value={profile?.planUsage?.deductible?.individual?.percentMet || 0}
                    className="w-full h-2"
                  />
                  <p className="text-xs text-gray-500 dark:text-slate-400">
                    {profile?.planUsage?.deductible?.individual?.percentMet?.toFixed(1) || '0.0'}% met
                  </p>
                </div>

                {/* Out-of-Pocket Max Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Out-of-Pocket Max</span>
                    <span className="text-sm text-gray-600 dark:text-slate-300">
                      ${profile?.planUsage?.outOfPocketMax?.individual?.met?.toFixed(2) || '0.00'} / ${profile?.planUsage?.outOfPocketMax?.individual?.amount?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                  <Progress
                    value={profile?.planUsage?.outOfPocketMax?.individual?.percentMet || 0}
                    className="w-full h-2"
                  />
                  <p className="text-xs text-gray-500 dark:text-slate-400">
                    {profile?.planUsage?.outOfPocketMax?.individual?.percentMet?.toFixed(1) || '0.0'}% met
                  </p>
                </div>

                {/* Optimize Costs Button */}
                <div className="pt-3 border-t border-gray-200 dark:border-slate-700">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {/* Placeholder */}}
                    className="w-full text-xs text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 hover:bg-emerald-50 dark:hover:bg-emerald-500/10"
                  >
                    <DollarSign className="h-3 w-3 mr-1" />
                    Optimize my costs
                  </Button>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Claims Section */}
      <Card>
        <Collapsible open={isClaimsOpen} onOpenChange={setIsClaimsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-violet-600 dark:text-violet-400" />
                  <span>Recent Claims</span>
                  <Badge variant="secondary" className="ml-2">
                    {recentClaims.length}
                  </Badge>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isClaimsOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              {recentClaims.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No recent claims found
                </p>
              ) : (
                <div className="space-y-3">
                  {recentClaims.map((claim, index) => (
                    <div
                      key={`${claim.claimId}-${index}`}
                      className="p-3 rounded-lg bg-gray-50 dark:bg-slate-800/80 border dark:border-violet-500/20 space-y-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-sm">
                            {claim.procedureDescription}
                          </h3>
                          <p className="text-xs text-gray-600 dark:text-slate-300">
                            {claim.providerName} • {claim.claimDate}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-slate-400">
                            <span>Amount: ${claim.billedAmount}</span>
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs",
                              claim.claimStatus === 'Paid' ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-500/20 dark:text-emerald-300' :
                              claim.claimStatus === 'Pending' ? 'bg-amber-100 text-amber-800 dark:bg-amber-500/20 dark:text-amber-300' :
                              'bg-red-100 text-red-800 dark:bg-red-500/20 dark:text-red-300'
                            )}>
                              {claim.claimStatus}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleClaimInquiry(claim.providerName, claim.claimDate)}
                          className="flex-1 text-xs"
                        >
                          Ask about this claim
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-slate-700">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {/* Placeholder */}}
                      className="w-full text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                    >
                      View All Claims
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Condition Management Section */}
      <Card>
        <Collapsible open={isConditionMgmtOpen} onOpenChange={setIsConditionMgmtOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HeartHandshake className="h-5 w-5 text-rose-600 dark:text-rose-400" />
                  <span>Condition Management</span>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isConditionMgmtOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="p-3 rounded-lg bg-blue-50 dark:bg-slate-800/80 border dark:border-blue-500/30 space-y-3">
                  <div>
                    <h3 className="font-medium text-sm flex items-center gap-2">
                      <HeartHandshake className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      Care management
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-slate-300 mt-1">
                      A nurse care manager can help you stay on track with care planning, appointments, and lifestyle changes. If you're not sure where to start, talking with a nurse care manager is a great first step.
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleScheduleCallBack()}
                      className="mt-2 text-xs border-blue-300 dark:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-500/10 text-blue-700 dark:text-blue-300"
                    >
                      Connect with a nurse
                    </Button>
                  </div>
                </div>

                <div className="p-3 rounded-lg bg-purple-50 dark:bg-slate-800/80 border dark:border-purple-500/30 space-y-3">
                  <div>
                    <h3 className="font-medium text-sm flex items-center gap-2">
                      <Video className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                      Virtual visits
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-slate-300 mt-1">
                      See GI specialists and other doctors from home, on your schedule.
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleScheduleTelehealth()}
                      className="mt-2 text-xs border-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-500/10 text-purple-700 dark:text-purple-300"
                    >
                      Schedule a virtual visit
                    </Button>
                  </div>
                </div>

                <div className="p-3 rounded-lg bg-orange-50 dark:bg-slate-800/80 border dark:border-orange-500/30 space-y-3">
                  <div>
                    <h3 className="font-medium text-sm flex items-center gap-2">
                      <Pill className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                      Easy medication management
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-slate-300 mt-1">
                      Set up refills, get delivery reminders, and check for drug interactions.
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleMedicationHelp()}
                      className="mt-2 text-xs border-orange-300 dark:border-orange-500 hover:bg-orange-50 dark:hover:bg-orange-500/10 text-orange-700 dark:text-orange-300"
                    >
                      Check refills and reminders
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Wellness and Prevention Section */}
      <Card>
        <Collapsible open={isWellnessOpen} onOpenChange={setIsWellnessOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
                  <span>Wellness and Prevention</span>
                </div>
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isWellnessOpen ? "rotate-180" : ""
                )} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="p-3 rounded-lg bg-cyan-50 dark:bg-slate-800/80 border dark:border-cyan-500/30 space-y-2">
                  <h3 className="font-medium text-sm">Personalized Exercise Plan</h3>
                  <p className="text-xs text-gray-600 dark:text-slate-300">
                    Custom fitness plan based on your health profile
                  </p>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {/* Placeholder */}}
                    className="text-xs border-cyan-300 dark:border-cyan-500 hover:bg-cyan-50 dark:hover:bg-cyan-500/10 text-cyan-700 dark:text-cyan-300"
                  >
                    View Plan
                  </Button>
                </div>

                <div className="p-3 rounded-lg bg-cyan-50 dark:bg-slate-800/80 border dark:border-cyan-500/30 space-y-2">
                  <h3 className="font-medium text-sm">Prevent Chronic Flu Reminders</h3>
                  <p className="text-xs text-gray-600 dark:text-slate-300">
                    Stay up to date with preventive care reminders
                  </p>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {/* Placeholder */}}
                    className="text-xs border-cyan-300 dark:border-cyan-500 hover:bg-cyan-50 dark:hover:bg-cyan-500/10 text-cyan-700 dark:text-cyan-300"
                  >
                    Set Reminders
                  </Button>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

    </div>
  );
}
