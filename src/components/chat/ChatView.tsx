'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback, RefObject } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, User, Bot, CornerDownLeft, Loader2, HandHeart, Mic, AudioLines, ScanHeart, ArrowBigUp, ArrowUp, Paperclip, Phone, PhoneOff, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useIsMobile } from '@/hooks/use-mobile';
import { useTranscripts } from '@/hooks/use-transcripts';
import type { Message, ChatResponsePart, Suggestion, ForYouItem, ShowConditionManagementData } from '@/types';
import dynamic from 'next/dynamic';
import { toast } from 'sonner';
import ReactMarkdown from 'react-markdown';
import ChatSuggestions from './ChatSuggestions';
import { initialStaticSuggestions } from '@/app/data/Static_Suggestions';
import { VoiceAssistantModal } from './VoiceAssistantModal';
import { TranscriptSelectionSheet } from './TranscriptSelectionSheet';
import { useRouter } from 'next/navigation';
import { WelcomeTextArea } from './WelcomeTextArea';
import { MessageFeedback } from './MessageFeedback';

// Performance-optimized dynamic imports with device-specific loading
const ProvidersList = dynamic(() => import('./ProvidersList'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading providers list" />
});

const AppointmentsList = dynamic(() => import('./AppointmentsList'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading appointments list" />
});

const ClaimsList = dynamic(() => import('./ClaimsList'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading claims list" />
});

const TelehealthAppointmentTimePicker = dynamic(() => import('./TelehealthAppointmentTimePicker'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-24" aria-label="Loading time picker" />
});

const TelehealthAppointmentCard = dynamic(() => import('./TelehealthAppointmentCard'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-40" aria-label="Loading appointment card" />
});

const PlanProgressCard = dynamic(() => import('./PlanProgressCard'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading plan progress" />
});

const PharmacyOrderStatusCard = dynamic(() => import('./PharmacyOrderStatusCard'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading pharmacy order status" />
});

const PrescriptionCard = dynamic(() => import('./PrescriptionCard'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-32" aria-label="Loading prescription information" />
});

const ConditionManagementCard = dynamic(() => import('./ConditionManagementCard'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-48" aria-label="Loading condition management information" />
});

// Lazy load ReactMarkdown only when needed for better performance
const LazyMarkdown = dynamic(() => import('react-markdown'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted rounded h-4 w-3/4" />
});

interface ChatViewProps {
  onOpenCareCanvas?: () => void;
}

export function ChatView({ onOpenCareCanvas }: ChatViewProps = {}) {
  const {
    currentThread,
    addMessage,
    isLoading: isContextLoading,
    markInitialMessageAsSent,
    addForYouItems,
    selectedTranscriptIds,
    clearSelectedTranscripts,
    isSending,
    setIsSending
  } = useChat();
  const { transcripts } = useTranscripts(); // Get all transcripts
  const [newMessage, setNewMessage] = useState('');
  const [isVoiceModalOpen, setIsVoiceModalOpen] = useState(false); // State for voice assistant modal
  const [isVoiceSessionActive, setIsVoiceSessionActive] = useState(false); // State for voice session status
  const [isTranscriptSheetOpen, setIsTranscriptSheetOpen] = useState(false); // State for transcript sheet
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const viewportRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isMobile = useIsMobile();
  const router = useRouter();

  // Performance optimization: Memoize device-specific configurations
  const deviceConfig = useMemo(() => ({
    isMobile,
    headerPadding: isMobile ? "p-4 pl-20" : "px-4 py-4",
    messagePadding: isMobile ? "px-4 py-0" : "px-4 py-0",
    buttonSize: isMobile ? "h-11 w-11" : "h-10 w-10",
    avatarSize: isMobile ? 'h-8 w-8' : 'h-8 w-8',
    inputAreaPadding: isMobile ? "p-3" : "p-4",
    autoFocus: !isMobile, // Avoid auto-focus on mobile to prevent keyboard popup
    showKeyboardHint: !isMobile,
    mobileReadyChatConversation: !isMobile ? 'h-full relative' : 'absolute bottom-0 top-15',
  }), [isMobile]);

  const formatMessagesForApi = (messages: Message[]) => {
    const formatted: { role: string; content: string }[] = [];
    for (const msg of messages) {
      // Skip system messages (voice call events) - don't send to AI
      if (msg.sender === 'system') {
        continue;
      }

      if (msg.sender === 'user') {
        formatted.push({ role: 'user', content: msg.content as string });
      } else if (msg.sender === 'ai' && Array.isArray(msg.content)) {
        for (const part of msg.content) {
          if (part.type === 'text') {
            if (part.content !== undefined) {
              formatted.push({ role: 'model', content: part.content as string });
            }
          } else if (part.type === 'function_call') {
            formatted.push({
              role: 'tool_code',
              content: JSON.stringify({ name: part.functionName, args: part.functionArgs }),
            });
          } else if (part.type === 'function_result') {
            formatted.push({
              role: 'tool_response',
              content: JSON.stringify(part.functionData),
            });
          }
          // Errors are typically frontend-only or handled by the backend,
          // so we don't send them back to the AI model as part of the conversation history.
        }
      }
    }
    return formatted;
  };

  const sendMessageToAI = async (messages: { role: string; content: any }[]) => {
    setIsSending(true);
    
    // Find selected transcripts' content
    const selectedTranscriptsContent = transcripts
      .filter(t => selectedTranscriptIds.includes(t.id))
      .map(t => t.transcript);

    try {
      const res = await fetch('/api/gemini-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messages,
          transcripts: selectedTranscriptsContent, // Add transcripts to the payload
        }),
      });
      if (!res.ok) throw new Error('AI failed to respond');
      const data: ChatResponsePart[] = await res.json(); // Expecting array of ChatResponsePart

      // Correctly check for 'enable_condition_management_features' function result and update For You items
      const conditionManagementResult = data.find(part => part.type === 'function_result' && part.functionName === 'enable_condition_management_features');
      if (conditionManagementResult && conditionManagementResult.functionData) {
        const conditionManagementData = conditionManagementResult.functionData as ShowConditionManagementData;
        addForYouItems(conditionManagementData.forYouItems); // Add new items to context HERE.
      }

      // Check for 'add_appointment_to_focus' function result and add to localStorage
      const appointmentResult = data.find(part => part.type === 'function_result' && part.functionName === 'add_appointment_to_focus');
      if (appointmentResult && appointmentResult.functionData) {
        const appointmentData = appointmentResult.functionData as any;

        // Get existing appointments from localStorage
        const existingAppointments = JSON.parse(localStorage.getItem('care-canvas-focus-appointments') || '[]');

        // Create new appointment object
        const newAppointment = {
          id: `appointment-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          type: appointmentData.type,
          provider: appointmentData.provider,
          specialty: appointmentData.specialty,
          date: appointmentData.date,
          time: appointmentData.time,
          createdAt: new Date().toISOString()
        };

        // Add to localStorage
        const updatedAppointments = [newAppointment, ...existingAppointments];
        localStorage.setItem('care-canvas-focus-appointments', JSON.stringify(updatedAppointments));

        // Dispatch custom event to notify useFocusAppointments hook
        window.dispatchEvent(new CustomEvent('focusAppointmentAdded'));
        
        // Show toast notification
        toast.success(`${appointmentData.type} appointment with ${appointmentData.provider} added to your focus!`);
      }

      addMessage(data, 'ai'); // Pass the structured data directly
      if (selectedTranscriptsContent.length > 0) {
        clearSelectedTranscripts(); // Clear selection after sending
        toast.info("Attached transcripts have been cleared from this chat session.");
      }
    } catch (e: any) {
      toast.error(e.message || 'Failed to get AI response');
    } finally {
      setIsSending(false);
    }
  };

  // Enhanced send message with performance optimization
  const handleSendMessage = useCallback(async () => {
    if (newMessage.trim() && currentThread) {
      const userMessage = newMessage.trim();
      addMessage(userMessage, 'user');
      setNewMessage('');
      textareaRef.current?.focus();

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  }, [newMessage, currentThread, addMessage, sendMessageToAI]);

  const handleSuggestionClick = useCallback(async (suggestionText: string) => {
    if (currentThread) {
      addMessage(suggestionText, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: suggestionText });

      await sendMessageToAI(messagesForApi);
    }
  }, [currentThread, addMessage, sendMessageToAI]);

  // Desktop keyboard shortcuts
  useEffect(() => {
    if (deviceConfig.isMobile) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Enter to send message
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleSendMessage();
      }
      // Escape to clear input
      if (e.key === 'Escape' && document.activeElement === textareaRef.current) {
        setNewMessage('');
      }
      // Focus input with '/' key (when not already focused)
      if (e.key === '/' && document.activeElement !== textareaRef.current) {
        e.preventDefault();
        textareaRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSendMessage, deviceConfig.isMobile]);

  const handleTimeSelection = async (selectedTime: string) => {
    if (currentThread) {
      const userMessage = `Selected time: ${selectedTime}`;
      addMessage(userMessage, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  };

  const handleAppointmentJoined = async () => {
    if (currentThread) {
      const userMessage = "Member has joined the telehealth appointment. ...Fast Foward";
      addMessage(userMessage, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  };

  const handleScheduleCallBack = async () => {
    if (currentThread) {
      const userMessage = "Schedule a nurse call";
      addMessage(userMessage, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  };

  const handleScheduleTelehealth = async () => {
    if (currentThread) {
      const userMessage = "I'd like to schedule a Telehealth appointment with a specialist";
      addMessage(userMessage, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  };

  const handleMedicationHelp = async () => {
    if (currentThread) {
      const userMessage = "I'd like to setup reminders for my medications";
      addMessage(userMessage, 'user');

      const messagesForApi = formatMessagesForApi(currentThread.messages);
      messagesForApi.push({ role: 'user', content: userMessage });

      await sendMessageToAI(messagesForApi);
    }
  };

  // Helper function to filter out hidden messages
  const filterVisibleMessages = (messages: Message[]): Message[] => {
    return messages.filter((msg: Message) => {
      // Hide the trigger message "Begin Personalized Condition Management Scenario" from the frontend
      if (msg.sender === 'user' && msg.content === 'drafting') {
        return false;
      }
      return true;
    });
  };

  // Helper function to determine if we should show a date separator
  const shouldShowDateSeparator = (currentMessage: Message, previousMessage: Message | null): boolean => {
    if (!previousMessage) return true; // Always show date for first message

    const currentDate = new Date(currentMessage.timestamp);
    const previousDate = new Date(previousMessage.timestamp);

    // Show date separator if messages are on different days
    const differentDay = currentDate.toDateString() !== previousDate.toDateString();

    // Also show if there's a significant time gap (more than 4 hours)
    const timeDifference = currentMessage.timestamp - previousMessage.timestamp;
    const fourHoursInMs = 4 * 60 * 60 * 1000;
    const significantGap = timeDifference > fourHoursInMs;

    return differentDay || significantGap;
  };

  // Helper function to extract suggestions from AI message parts
  const extractSuggestions = (parts: ChatResponsePart[]): Suggestion[] => {
    const suggestionParts = parts.filter(part =>
      part.type === 'function_result' && part.functionName === 'show_suggestions'
    );

    if (suggestionParts.length > 0) {
      return suggestionParts[0].functionData as Suggestion[];
    }

    return [];
  };

  // Enhanced scroll behavior with subtle scroll for new AI responses
  const previousMessagesLength = useRef<number>(0);
  const lastMessageSender = useRef<string | null>(null);

  useEffect(() => {
    if (viewportRef.current && currentThread?.messages) {
      const scrollElement = viewportRef.current;
      const scrollBehavior = deviceConfig.isMobile ? 'auto' : 'smooth';
      const currentMessages = currentThread.messages;
      const currentLength = currentMessages.length;
      const previousLength = previousMessagesLength.current;

      // Use requestAnimationFrame for better performance
      requestAnimationFrame(() => {
        // Check if this is a new message being added
        const hasNewMessage = currentLength > previousLength && currentLength > 0;
        const lastMessage = hasNewMessage ? currentMessages[currentLength - 1] : null;

        // Check if this is a new AI message being added
        const isNewAIMessage = hasNewMessage && lastMessage?.sender === 'ai';

        // Check if this is the initial load or user sent a message
        const isInitialLoadOrUserMessage = previousLength === 0 ||
                                          (hasNewMessage && lastMessage?.sender === 'user');

        if (isNewAIMessage) {
          // Subtle scroll for new AI responses - scroll just enough to indicate new content
          const currentScrollTop = scrollElement.scrollTop;
          const scrollHeight = scrollElement.scrollHeight;
          const clientHeight = scrollElement.clientHeight;

          // Calculate a subtle scroll amount (about 100-150px or 20% of viewport height, whichever is smaller)
          const subtleScrollAmount = Math.min(150, clientHeight * 0.2);
          const newScrollPosition = Math.min(currentScrollTop + subtleScrollAmount, scrollHeight - clientHeight);

          scrollElement.scrollTo({
            top: newScrollPosition,
            behavior: scrollBehavior,
          });
        } else if (isInitialLoadOrUserMessage) {
          // Full scroll to bottom for initial loads and user messages
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: scrollBehavior,
          });
        }

        // Update tracking variables
        previousMessagesLength.current = currentLength;
        if (currentLength > 0) {
          lastMessageSender.current = currentMessages[currentLength - 1].sender;
        }
      });
    }
  }, [currentThread?.messages, deviceConfig.isMobile]);

  // Effect to automatically send initial message for topic-initiated chats
  useEffect(() => {
    if (
      currentThread &&
      currentThread.messages.length === 1 &&
      currentThread.messages[0].sender === 'user' &&
      !isSending &&
      !currentThread.initialMessageSent
    ) {
      markInitialMessageAsSent(currentThread.id);
      const initialUserMessage = currentThread.messages[0].content as string;
      const messagesForApi = [{ role: 'user', content: initialUserMessage }];
      sendMessageToAI(messagesForApi);
    }
  }, [currentThread, isSending, sendMessageToAI, markInitialMessageAsSent]);

  // Enhanced scroll to bottom function - used for manual scrolling or specific cases
  const scrollToBottom = useCallback(() => {
    if (viewportRef.current) {
      viewportRef.current.scrollTo({
        top: viewportRef.current.scrollHeight,
        behavior: deviceConfig.isMobile ? 'auto' : 'smooth',
      });
    }
  }, [deviceConfig.isMobile]);

  if (isContextLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-muted-foreground">Loading your chats...</p>
      </div>
    );
  }

  if (!currentThread) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-4 sm:px-20" role="main">
        <h1 id="VC_h1" className="text-center text-zinc-800 leading-relaxed">
          Virtual Assistant
        </h1>

        <h2 className="text-center text-zinc-800">
          Your personalized healthcare journey starts here.
        </h2>
        <WelcomeTextArea placeholder="Ask me anything..." />

      </div>
    );
  }

  return (
    <div className={cn("flex flex-col min-h-0", deviceConfig.mobileReadyChatConversation)} role="main" aria-label="Chat conversation">
      <div className="flex-1 min-h-0">
        <ScrollArea
          ref={scrollAreaRef}
          viewportRef={viewportRef as RefObject<HTMLDivElement>}
          className={cn(
            "h-full overflow-y-auto space-y-2 scroll-smooth no-scrollbar",
            deviceConfig.messagePadding
          )}
          role="log"
          aria-live="polite"
          aria-label="Chat messages"
          tabIndex={0}
        >
          {(() => {
            const visibleMessages = filterVisibleMessages(currentThread.messages);
            return visibleMessages.map((msg: Message, index: number) => {
              const previousMessage = index > 0 ? visibleMessages[index - 1] : null;
              const showDateSeparator = shouldShowDateSeparator(msg, previousMessage);

            return (
              <React.Fragment key={msg.id}>
                {/* Date Separator */}
                {showDateSeparator && (
                  <div className="flex justify-center my-6">
                    <div className="px-3 py-1 bg-muted/50 text-muted-foreground rounded-full text-xs font-medium">
                      {new Date(msg.timestamp).toLocaleDateString([], {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                  </div>
                )}

                {/* Handle system messages (voice call events) and voice assistant messages differently */}
                {msg.sender === 'system' || msg.sender === 'voice_assistant' ? (
                  msg.sender === 'voice_assistant' ? (
                    // Custom design for voice assistant messages
                    <div className="flex justify-center my-6">
                      <div className="max-w-[80%] w-full">
                        {/* Title section */}
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <HandHeart className="h-4 w-4 text-purple-600 dark:text-purple-400" aria-hidden="true" />
                          <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                            Message from your voice assistant:
                          </span>
                        </div>

                        {/* Message content */}
                        <div className="bg-purple-50 dark:bg-purple-950/50 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                          <p className="text-purple-800 dark:text-purple-200 text-sm leading-relaxed">
                            {msg.content as string}
                          </p>
                          <time
                            className="text-xs text-purple-600 dark:text-purple-400 mt-2 block"
                            dateTime={new Date(msg.timestamp).toISOString()}
                          >
                            {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </time>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Original system message design
                    <div
                      className="flex justify-center my-4"
                      role="status"
                      aria-label={`System message: ${msg.content}`}
                    >
                      <div
                        className={cn(
                          "flex items-center gap-2 px-3 py-2 rounded-full text-xs font-medium bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300",
                          msg.messageType === 'voice_call_end' && "cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
                        )}
                        onClick={msg.messageType === 'voice_call_end' ? () => setIsTranscriptSheetOpen(true) : undefined}
                        role={msg.messageType === 'voice_call_end' ? "button" : undefined}
                        tabIndex={msg.messageType === 'voice_call_end' ? 0 : undefined}
                        onKeyDown={msg.messageType === 'voice_call_end' ? (e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setIsTranscriptSheetOpen(true);
                          }
                        } : undefined}
                        aria-label={msg.messageType === 'voice_call_end' ? "Voice call ended - Click to view transcript" : `System message: ${msg.content}`}
                      >
                        {msg.messageType === 'voice_call_start' ? (
                          <Phone className="h-3 w-3" aria-hidden="true" />
                        ) : msg.messageType === 'voice_call_end' ? (
                          <PhoneOff className="h-3 w-3" aria-hidden="true" />
                        ) : null}
                        <span>{msg.content as string}</span>
                        <time
                          className="text-blue-600 dark:text-blue-400"
                          dateTime={new Date(msg.timestamp).toISOString()}
                        >
                          {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </time>
                        {msg.messageType === 'voice_call_end' && (
                          <ChevronRight className="h-3 w-3 ml-1" aria-hidden="true" />
                        )}
                      </div>
                    </div>
                  )
                ) : (
                  // Handle regular user and AI messages
                  <>
                    <div
                      className={cn(
                        'flex items-start gap-3 my-6',
                        msg.sender === 'user' ? 'justify-end' : 'justify-start'
                      )}
                      role="article"
                      aria-label={`${msg.sender === 'user' ? 'Your' : 'AI'} message ${index + 1}`}
                    >
                      {msg.sender === 'ai' && (
                        <Avatar className={cn(deviceConfig.avatarSize)} aria-hidden="true">
                          <AvatarFallback><HandHeart size={18} aria-label="AI Assistant" /></AvatarFallback>
                        </Avatar>
                      )}
                      <div
                        className={cn(
                          'rounded-lg p-3 text-sm shadow-sm',
                          // Consistent message sizing for better chat experience
                          'max-w-[80%]',
                          msg.sender === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        )}
                        id={msg.sender === 'ai' ? 'VC_Response' : undefined}
                      >
                        {msg.sender === 'ai' ? (
                          <AIMessageRenderer
                            parts={msg.content as ChatResponsePart[]}
                            onTimeSelect={handleTimeSelection}
                            onSuggestionClick={handleSuggestionClick}
                            onJoinNow={handleAppointmentJoined}
                            onScheduleCallBack={handleScheduleCallBack}
                            onScheduleTelehealth={handleScheduleTelehealth}
                            onMedicationHelp={handleMedicationHelp}
                            onOpenCareCanvas={onOpenCareCanvas}
                            excludeSuggestions={true}
                          />
                        ) : (
                          <p className='whitespace-pre-wrap'>{msg.content as string}</p>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          <time
                            className={cn('text-xs', msg.sender === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground/70')}
                            dateTime={new Date(msg.timestamp).toISOString()}
                          >
                            {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </time>
                          {msg.sender === 'ai' && currentThread && (
                            <MessageFeedback
                              messageId={msg.id}
                              threadId={currentThread.id}
                              className="ml-2"
                            />
                          )}
                        </div>
                      </div>
                      {msg.sender === 'user' && (
                        <Avatar className={cn(deviceConfig.avatarSize)} aria-hidden="true">
                          <AvatarFallback><User size={18} aria-label="You" /></AvatarFallback>
                        </Avatar>
                      )}
                    </div>

                    {/* Render suggestions outside and below AI message */}
                    {msg.sender === 'ai' && Array.isArray(msg.content) && (() => {
                      const suggestions = extractSuggestions(msg.content as ChatResponsePart[]);
                      if (suggestions.length > 0) {
                        return (
                          <div className="flex justify-start ml-11 mb-4">
                            <div className="max-w-[80%]">
                              <ChatSuggestions
                                suggestions={suggestions}
                                onSuggestionClick={handleSuggestionClick}
                              />
                            </div>
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </>
                )}
              </React.Fragment>
            );
          });
          })()}
          {currentThread && filterVisibleMessages(currentThread.messages).length === 0 && !isSending && (
            <div className="flex flex-col items-center justify-center py-8 text-center px-4 sm:px-20">
              <div className="flex items-center justify-center mb-4 ">
                <p className=" mb-4">I can help you find providers, manage appointments, check claims, track your plan progress, and handle pharmacy orders. How can I assist you today?</p>
              </div>
              <ChatSuggestions
                suggestions={initialStaticSuggestions}
                onSuggestionClick={handleSuggestionClick}
              />
            </div>
          )}
          {isSending && currentThread.messages.some((m: Message) => m.sender === 'user') && currentThread.messages[currentThread.messages.length - 1].sender === 'user' && (
            <div className="flex items-start gap-3 justify-start" role="status" aria-live="polite">
              <Avatar className='h-8 w-8' aria-hidden="true">
                <AvatarFallback><Bot size={18} /></AvatarFallback>
              </Avatar>
              <div className="bg-muted rounded-lg p-3 text-sm shadow-sm flex items-center">
                <Loader2 className="h-4 w-4 animate-spin mr-2" aria-hidden="true" />
                <span>Your virtual Assistant is thinking...</span>
              </div>
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Enhanced input area with accessibility and mobile optimizations */}
      <div className={cn(
        "border-none shrink-0",
        deviceConfig.inputAreaPadding
      )}>
        <form
          onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}
          className="flex items-end flex-col gap-2 p-2 rounded-3xl bg-white dark:bg-zinc-800 shadow-xl"
          role="form"
          id="VC_Input"
          aria-label="Send message form"
        >
          <Textarea
            ref={textareaRef}
            placeholder={deviceConfig.isMobile ? "Ask me anything..." : "Ask me anything..."}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            className="flex-grow min-h-[40px] resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-base p-3 shadow-none"
            autoFocus={deviceConfig.autoFocus}
            aria-label="Message input"
            aria-describedby={deviceConfig.showKeyboardHint ? "keyboard-hint" : undefined}
            maxLength={2000}
            rows={1}
          />
          <div className="flex flex-row items-center justify-between w-full gap-2 mb-2">
            <div className="items-center gap-2 pl-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="relative border rounded-full whitespace-nowrap"
              onClick={() => setIsTranscriptSheetOpen(true)}
              aria-label="Attach transcripts"
            >
              <Paperclip className="h-5 w-5 md:mr-2" />
              <span className="hidden md:inline">Attach Transcripts</span>
              {selectedTranscriptIds.length > 0 && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs">
                  {selectedTranscriptIds.length}
                </span>
              )}
            </Button>
            </div>
            <div className="flex flex-row items-center gap-4">
            <Button
              type="button"
              size="sm"
              className={cn(
                "rounded-full whitespace-nowrap transition-all duration-200",
                isVoiceSessionActive
                  ? "bg-green-100 text-green-800 hover:bg-green-200 animate-pulse"
                  : "bg-blue-100 text-blue-800 hover:bg-blue-200"
              )}
              aria-label={isVoiceSessionActive ? "Voice Assistant (Active)" : "Voice Assistant"}
              onClick={() => setIsVoiceModalOpen(true)}
            >
              <AudioLines className={cn(
                "h-5 w-5 md:mr-2",
                isVoiceSessionActive && "animate-pulse"
              )} />
              <span className="hidden md:inline">
                {isVoiceSessionActive ? "Voice Active" : "Voice Assistant"}
              </span>
            </Button>
            <Button
              type="submit"
              id="send-button"
              size="icon"
              disabled={!newMessage.trim() || isSending}
              className={cn(
                deviceConfig.buttonSize,
                "transition-all duration-200"
              )}
              aria-label={isSending ? "Sending message" : "Send message"}
            >
              {isSending ? (
                <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              ) : (
                <ArrowUp className="h-4 w-4" aria-hidden="true" />
              )}
            </Button>
            </div>
          </div>
        </form>

        <VoiceAssistantModal
          isOpen={isVoiceModalOpen}
          onClose={() => setIsVoiceModalOpen(false)}
          formattedMessages={currentThread ? formatMessagesForApi(currentThread.messages) : []}
          isSessionActive={isVoiceSessionActive}
          onSessionStateChange={setIsVoiceSessionActive}
          threadId={currentThread?.id} // Pass the current thread ID for context-aware events
        />
        <TranscriptSelectionSheet isOpen={isTranscriptSheetOpen} onOpenChange={setIsTranscriptSheetOpen} />

        

        {deviceConfig.isMobile && (
          <div className="flex items-center justify-center mt-2">
            <p className="text-xs text-muted-foreground text-center">
              {/* 💡 Tip: Swipe left on sidebar to close */}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

function AIMessageRenderer({
  parts,
  onTimeSelect,
  onSuggestionClick,
  onJoinNow,
  onScheduleCallBack,
  onScheduleTelehealth,
  onMedicationHelp,
  onOpenCareCanvas,
  excludeSuggestions = false,
}: {
  parts: ChatResponsePart[];
  onTimeSelect: (time: string) => void;
  onSuggestionClick: (suggestionText: string) => void;
  onJoinNow: () => void;
  onScheduleCallBack: () => void;
  onScheduleTelehealth: () => void;
  onMedicationHelp: () => void;
  onOpenCareCanvas?: () => void;
  excludeSuggestions?: boolean;
}) {
  return (
    <>
      {parts.map((part, i) => {
        if (part.type === 'text') {
          return (
            <div key={i} className="leading-loose dark:prose-invert max-w-none pb-4">
              <LazyMarkdown>{part.content}</LazyMarkdown>
            </div>
          );
        } else if (part.type === 'function_result') {
          // Render the specific UI component based on functionName and functionData
          switch (part.functionName) {
            case 'show_providers':
              return <div key={i} className="pb-4"><ProvidersList data={part.functionData} /></div>;
            case 'show_appointments':
              return <div key={i} className="pb-4"><AppointmentsList data={part.functionData} /></div>;
            case 'show_claims':
              return <div key={i} className="pb-4"><ClaimsList data={part.functionData} /></div>;
            case 'show_telehealth_time_picker':
              return <div key={i} className="pb-4"><TelehealthAppointmentTimePicker onTimeSelect={onTimeSelect} /></div>;
            case 'show_telehealth_appointment_card':
              return <div key={i} className="pb-4"><TelehealthAppointmentCard data={part.functionData} onJoinNow={onJoinNow} /></div>;
            case 'show_plan_progress':
              return <div key={i} className="pb-4"><PlanProgressCard data={part.functionData} /></div>;
            case 'show_pharmacy_order_status':
              return <div key={i} className="pb-4"><PharmacyOrderStatusCard data={part.functionData} /></div>;
            case 'show_prescriptions':
              return <div key={i} className="pb-4"><PrescriptionCard data={part.functionData} /></div>;
            case 'show_condition_management':
              return <div key={i} className="pb-4"><ConditionManagementCard onScheduleCallBack={onScheduleCallBack} onScheduleTelehealth={onScheduleTelehealth} onMedicationHelp={onMedicationHelp} onOpenCareCanvas={onOpenCareCanvas} /></div>;
            case 'enable_condition_management_features': // Handle new function result
              // The ChatView's sendMessageToAI handles the state update.
              // This component just needs to render nothing (or a hidden element)
              // for this specific function result type.
              return <span key={i} className="hidden" aria-hidden="true">Condition management enrolled.</span>;
            case 'add_appointment_to_focus': // Handle appointment addition to Focus
              // The ChatView's sendMessageToAI handles the localStorage update.
              // This component just needs to render nothing for this function result type.
              return <span key={i} className="hidden" aria-hidden="true">Appointment added to Focus.</span>;
            case 'show_suggestions':
              // Skip rendering suggestions if excludeSuggestions is true
              if (excludeSuggestions) {
                return null;
              }
              return (
                <div key={i} className="pt-2 pb-2">
                  <ChatSuggestions
                    suggestions={part.functionData as Suggestion[]}
                    onSuggestionClick={onSuggestionClick}
                  />
                </div>
              );
            default:
              return <span key={i}>[Unknown function result: {part.functionName}]</span>;
          }
        } else if (part.type === 'function_call') {
          // Optional: Display a loading indicator or message for the function call
          // return <span key={i}>AI is processing {part.functionName}...</span>;
        } else if (part.type === 'error') {
          return <span key={i} className="text-red-500">Error: {part.errorMessage}</span>;
        }
        return null;
      })}
    </>
  );
}