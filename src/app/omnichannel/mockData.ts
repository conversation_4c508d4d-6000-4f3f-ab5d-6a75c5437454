import { Thread, Message, Transcript, ChatResponsePart } from '@/types';

// Helper function to generate unique IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Helper function to get current timestamp
const getCurrentTimestamp = () => Date.now();

// Mock chat conversation data
export const createMockChatThread = (): Thread => {
  const threadId = generateId();
  // Start the conversation 7 days ago to show date separators
  const baseTimestamp = getCurrentTimestamp() - (7 * 24 * 60 * 60 * 1000); // 7 days ago

  const messages: Message[] = [
    // --- Part 1: Initial Symptom & Scheduling (Chat) - 7 days ago ---
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp,
      content: "Hi, I think I have the flu. I've been coughing a lot and feel short of breath."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (1 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "I'm sorry to hear you're not feeling well, <PERSON>. Based on your symptoms, getting checked out is a good idea."
        },
        {
          type: 'text',
          content: "I'd recommend either a Telehealth visit or going to an in-network Urgent Care center. I can help you find options for either. What works best for you?"
        }
      ] as ChatResponsePart[]
    },
    // --- Channel Switch to Voice ---
    {
      id: generateId(),
      sender: 'system',
      timestamp: baseTimestamp + (2 * 60 * 1000), // 2 mins later
      content: 'Voice call started',
      messageType: 'voice_call_start'
    },
    {
      id: generateId(),
      sender: 'system',
      timestamp: baseTimestamp + (7 * 60 * 1000), // 5-minute call ends
      content: 'Voice call ended',
      messageType: 'voice_call_end'
    },
    // --- Follow-up from Voice Call (Chat) ---
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (8 * 60 * 1000), // 1 min after call
      content: [
          {
              type: 'text',
              content: "As promised, here are a few questions you might want to ask the doctor during your visit to help you get the most out of it:\n1. Based on my symptoms, what do you think is the most likely cause?\n2. Are there any specific over-the-counter medications that could help with this cough?\n3. What warning signs should I look for that might indicate this is something more serious?\n4. Is this contagious, and if so, for how long?\n5. Are there any next steps or follow-ups I should expect after this visit?"
          }
      ] as ChatResponsePart[]
    },
    // --- Part 2: Post-Visit Follow-up (Chat) - Same day, later ---
    // Simulating time passing for the appointment, this message is later the same day.
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000), // 6 hours later (same day)
      content: [
        {
          type: 'text',
          content: "Hi Taylor, I see you've completed your visit at Community Urgent Care. Your doctor has diagnosed you with Asthma and prescribed an Albuterol inhaler."
        },
        {
          type: 'text',
          content: "I can have this prescription filled via CarelonRX and delivered directly to your home. Would you like me to set up home delivery?"
        }
      ] as ChatResponsePart[]
    },
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (1 * 60 * 1000), // 1 min later
      content: "Wow, yes please. That would be a huge help."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (2 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "Great. Your Albuterol inhaler is being processed by CarelonRX for home delivery. Additionally, your doctor referred you to a Pulmonologist. I can confirm your Utlization Management (UM) referral has been instantly approved. Would you like to schedule an appointment with a specialist now?"
        }
      ] as ChatResponsePart[]
    },
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (3 * 60 * 1000), // 1 min later
      content: "That was fast! Yes, let's do it."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (4 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "Here are a few in-network Pulmonologists available next week:"
        },
        {
          type: 'function_result', // Simulating a UI card with providers
          functionName: 'show_providers',
          functionData: [
              { id: 'p1', name: 'Dr. Eva Rostova', specialty: 'Pulmonologist', distance: '5 miles', cost: '$40', rating: 4.9, reviewsCount: 182, address: '123 Health Drive', phone: '************' },
              { id: 'p2', name: 'Dr. Ben Carter', specialty: 'Pulmonologist', distance: '8 miles', cost: '$40', rating: 4.7, reviewsCount: 98, address: '456 Wellness Way', phone: '************' }
          ]
        }
      ] as ChatResponsePart[]
    },
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (5 * 60 * 1000), // 1 min later
      content: "Please schedule me with Dr. Eva Rostova."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (6 * 60 * 60 * 1000) + (6 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "Excellent. You are scheduled with Dr. Rostova for next Tuesday at 10:00 AM. I've also confirmed that all your medical records have been securely sent to her office. You'll get a reminder the day before."
        }
      ] as ChatResponsePart[]
    },
    // --- Part 3: Day-of-Appointment Transportation Issue - 6 days later ---
    // Simulating time passing until the day of the appointment (Tuesday).
    {
      id: generateId(),
      sender: 'system',
      timestamp: baseTimestamp + (6 * 24 * 60 * 60 * 1000) + (9 * 60 * 60 * 1000), // 6 days later + 9 AM (appointment day)
      content: 'Voice call started',
      messageType: 'voice_call_start'
    },
    {
      id: generateId(),
      sender: 'system',
      timestamp: baseTimestamp + (6 * 24 * 60 * 60 * 1000) + (9 * 60 * 60 * 1000) + (4 * 60 * 1000), // 4-minute call ends
      content: 'Voice call ended',
      messageType: 'voice_call_end'
    }
  ];

  return {
    id: threadId,
    name: "Virtual Assistant - Asthma Care",
    messages,
    createdAt: baseTimestamp,
    lastModifiedAt: baseTimestamp + (6 * 24 * 60 * 60 * 1000) + (9 * 60 * 60 * 1000) + (4 * 60 * 1000), // Updated to match final voice call end
    initialMessageSent: true
  };
};

// Mock transcript data - First call (before the chat)
export const createMockTranscript1 = (): Transcript => {
  // Align with "Channel Switch to Voice" comment in the chat thread
  // This should match the timestamp of the first voice_call_start system message
  const baseTimestamp = getCurrentTimestamp() - (7 * 24 * 60 * 60 * 1000); // Same base as chat thread (7 days ago)
  const callStartTime = baseTimestamp + (2 * 60 * 1000); // 2 mins later (matches voice_call_start)
  
  return {
    id: generateId(),
    summary: "Taylor, called to find and schedule an urgent care appointment for her flu-like symptoms. Booked an appointment at Community Urgent Care and assistant offered to send follow-up questions to the chat.",
    transcript: `AI: Hello Taylor, this is Liz. I see you were just chatting with us about feeling unwell. How can I help you continue?

User: Hi Liz. Yes, I'd like to find an Urgent Care center nearby.

AI: Of course. I see a highly-rated, in-network option, 'Community Urgent Care on Main Street,' which is about 10 minutes from you. They have immediate availability. Would you like me to book an appointment there for you?

User: That sounds perfect, yes please.

AI: You're all set. I've scheduled you for 4:00 PM today. To help you prepare for your visit, I'll send a list of suggested questions to ask the doctor right back to our chat.

User: Oh, that would be very helpful. Thank you.

AI: You're welcome. I hope you feel better soon.`,
    startedAt: new Date(callStartTime).toISOString(),
    endedAt: new Date(callStartTime + (5 * 60 * 1000)).toISOString(), // 5 minute call
    endedReason: "customer-ended-call",
    recordingUrls: {
      mono: "https://example.com/mock-recording-1-mono.wav",
      stereo: "https://example.com/mock-recording-1-stereo.wav"
    }
  };
};

// Mock transcript data - Second call (after the chat)
export const createMockTranscript2 = (): Transcript => {
  // Align with "Part 3: Day-of-Appointment Transportation Issue" comment
  // This should match the timestamp of the second voice_call_start system message
  const baseTimestamp = getCurrentTimestamp() - (7 * 24 * 60 * 60 * 1000); // Same base as chat thread (7 days ago)
  const callStartTime = baseTimestamp + (6 * 24 * 60 * 60 * 1000) + (9 * 60 * 60 * 1000); // 6 days later + 9 AM (matches voice_call_start)

  return {
    id: generateId(),
    summary: "Taylor, called in a panic because her car broke down before her specialist appointment. Assistant utilized her transportation benefit to schedule an Uber for her.",
    transcript: `AI: Hello Taylor, this is Liz. How can I help you?

User: Hi Liz, I'm in a panic. My car is in the shop for repair, and I'm going to miss my 10 AM appointment with Dr. Rostova.

AI: I understand, that's stressful. Please don't worry. I see that your plan includes transportation benefits. I can arrange for an Uber to pick you up in the next 15-20 minutes, which should get you there on time.

User: You can do that? That's amazing, a total lifesaver!

AI: Of course. I've just scheduled your ride. You will receive a text message with the driver's details shortly. Is there anything else I can assist you with?

User: No, that's it. Thank you so much, Liz.

AI: You're very welcome, Taylor. I'm glad I could help. Have a great appointment!`,
    startedAt: new Date(callStartTime).toISOString(),
    endedAt: new Date(callStartTime + (4 * 60 * 1000)).toISOString(), // 4 minute call
    endedReason: "customer-ended-call",
    recordingUrls: {
      mono: "https://example.com/mock-recording-2-mono.wav",
      stereo: "https://example.com/mock-recording-2-stereo.wav"
    }
  };
};

// Mock Personalized Condition Management conversation data
export const createMockConditionManagementThread = (): Thread => {
  const threadId = generateId();
  const baseTimestamp = getCurrentTimestamp() - (1 * 60 * 60 * 1000); // 1 hour ago

  const messages: Message[] = [
    // User initiates the scenario
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp,
      content: "Begin Personalized Condition Management Scenario"
    },
    // AI responds with initial outreach
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (1 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "Hi, Taylor. Thanks for taking the next step with your care.\n\nWe received a recent claim that suggests you may be living with Crohn's disease. I'm here to walk you through some tools and resources that can help make managing your Crohn's a little less overwhelming.\n\nBefore we get started, could you please confirm you've been diagnosed with Crohn's disease?"
        },
        {
          type: 'function_result',
          functionName: 'show_suggestions',
          functionData: [
            { text: "Yes, I've been diagnosed" },
            { text: "No, I haven't" }
          ]
        }
      ] as ChatResponsePart[]
    },
    // User confirms diagnosis
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (2 * 60 * 1000), // 1 min later
      content: "Yes, I've been diagnosed"
    },
    // AI responds with step 2 - condition management details
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (3 * 60 * 1000), // 1 min later
      content: [
        {
          type: 'text',
          content: "Thanks for confirming, Taylor.\n\nManaging a chronic condition can feel complex and overwhelming. Let's explore how your plan can help make things a bit more manageable.\n\nHere's some of the support your plan offers for managing Crohn's disease:"
        },
        {
          type: 'function_result',
          functionName: 'show_condition_management',
          functionData: {
            trigger: 'diagnosis_confirmed',
            nurseLineDescription: "Your go-to nurse for support, guidance, and answers. They'll help you navigate care, appointments, and lifestyle changes.",
            telehealthTitle: "Specialist telehealth access",
            telehealthDescription: "Virtual visits with GI specialists, plus help with scheduling and getting the most from each appointment.",
            medicationTitle: "Easy medication management",
            medicationDescription: "Set up refills, get delivery reminders, and avoid drug interactions—all in one place.",
            careTeamTitle: "Connected care support",
            careTeamDescription: "Your symptoms, meds, and care plans stay aligned across your healthcare team."
          }
        },
        {
          type: 'function_result',
          functionName: 'show_suggestions',
          functionData: [
            { text: "Schedule call with a nurse care manager" },
            { text: "Remind me in 3 days" }
          ]
        }
      ] as ChatResponsePart[]
    }
  ];

  return {
    id: threadId,
    name: "Personalized Condition Management - Crohn's",
    messages,
    createdAt: baseTimestamp,
    lastModifiedAt: baseTimestamp + (3 * 60 * 1000),
    initialMessageSent: true
  };
};
