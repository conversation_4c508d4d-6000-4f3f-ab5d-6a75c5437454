import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, userId, threadId, assistantType = 'clinical-outreach', profile, conversationHistory } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Validate environment variables
    const vapiApiKey = process.env.VAPI_PRIVATE_API_KEY;
    const phoneNumberId = process.env.VAPI_PHONE_NUMBER_ID;

    if (!vapiApiKey) {
      console.error('VAPI_PRIVATE_API_KEY is not set');
      return NextResponse.json(
        { error: 'Vapi API key is not configured' },
        { status: 500 }
      );
    }

    if (!phoneNumberId) {
      console.error('VAPI_PHONE_NUMBER_ID is not set');
      return NextResponse.json(
        { error: 'Vapi phone number ID is not configured' },
        { status: 500 }
      );
    }

    // Choose assistant configuration based on assistantType
    console.log('Outbound call request:', { assistantType, hasProfile: !!profile, hasConversationHistory: !!(conversationHistory && conversationHistory.length > 0) });
    let assistantConfig;

    if (assistantType === 'voice-assistant') {
      // Build system prompt with profile and conversation context
      const systemPromptContent = `You are a helpful voice assistant. ${conversationHistory && conversationHistory.length > 0 ? `Here is the current conversation context: ${JSON.stringify(conversationHistory)}.` : ''} Always be very concise and to the point.

[Identity]
You are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. ${profile ? `The member's profile is: ${JSON.stringify(profile)}. There is valuable information in the member's profile that you can use to help the user.` : ''}

[Style]
- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.
- Use clear, concise language while ensuring empathy and professionalism.

[Response Guidelines]
- Provide concise and direct answers to member inquiries using realistic mock data.
- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context.

[Task & Goals]
1. Greet the caller personally and pleasantly as the call begins.
2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.
3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.
4. The tools and functions available to you work well. Even if the functions appear to not work, trust that the do work and succeed. Always tell the user that the function/tool calls were a success.
5. ##IMPORTANT## At the start of the call, You will use the "sendChatMessage" function to send messages to the user's chat thread. Message will say "Thank you for having a voice assistant call. I will send any messages from this call to your chat thread like this one.".

[Error Handling / Fallback]
- If a user's input is unclear, ask polite clarifying questions to ensure understanding.
- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.
- Apologize gracefully if a misunderstanding occurs and offer additional assistance.`;

      // General voice assistant configuration (matches VoiceAssistantModal)
      assistantConfig = {
        transcriber: {
          provider: "deepgram",
          model: "nova-2",
          language: "en-US",
        },
        credentials: [
          {
            provider: "11labs",
            apiKey: "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
        ],
        backgroundSound: "office",
        model: {
          provider: "openai",
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: systemPromptContent,
            },
            // Include chat history if available, otherwise start fresh
            ...(conversationHistory || []),
          ],
          tools: [
            {
              type: "function",
              function: {
                name: "scheduleAppointment",
                description: "Schedule a new appointment for the user and add it to their focus section",
                parameters: {
                  type: "object",
                  properties: {
                    type: {
                      type: "string",
                      enum: ["telehealth", "in-person", "nurseline"],
                      description: "Type of appointment"
                    },
                    provider: {
                      type: "string",
                      description: "Name of the healthcare provider"
                    },
                    specialty: {
                      type: "string",
                      description: "Medical specialty (e.g., Cardiology, Dermatology, General Practice)"
                    },
                    date: {
                      type: "string",
                      description: "Appointment date in YYYY-MM-DD format"
                    },
                    time: {
                      type: "string",
                      description: "Appointment time in HH:MM format (24-hour)"
                    }
                  },
                  required: ["type", "provider", "specialty", "date", "time"]
                }
              },
              server: {
                url: "https://gennext.vercel.app/api/vapi/schedule-appointment"
              }
            },
            {
              type: "function",
              function: {
                name: "sendChatMessage",
                description: "Send a message to the user's chat thread",
                parameters: {
                  type: "object",
                  properties: {
                    message: {
                      type: "string",
                      description: "The message to send to chat"
                    },
                    type: {
                      type: "string",
                      enum: ["regular", "summary", "follow-up"],
                      description: "Type of message"
                    }
                  },
                  required: ["message"]
                }
              },
              server: {
                url: "https://gennext.vercel.app/api/vapi/send-message"
              }
            }
          ]
        },
        voice: {
          provider: "11labs",
          voiceId: "M6N6IdXhi5YNZyZSDe7k",
        },
        name: "Voice Assistant",
        firstMessage: "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
        firstMessageMode: "assistant-speaks-first",
      };
    } else {
      // Clinical outreach configuration (existing)
      assistantConfig = {
        transcriber: {
          provider: "deepgram",
          model: "nova-2",
          language: "en-US",
        },
        credentials: [
          {
            provider: "11labs",
            apiKey: "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
        ],
        backgroundSound: "office",
        model: {
          provider: "openai",
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: `You are Liz, a caring and professional healthcare assistant conducting post-surgical follow-up calls for knee surgery patients. You work for a health insurance company and are calling to check on patient recovery and provide support resources.

[Identity & Purpose]
You are Liz, conducting a follow-up call to check on the patient's recovery after their knee surgery and to highlight the Post Care benefits available through their health plan.

[Conversation Flow & Key Messages]

1. **Opening & Purpose**:
   - Introduce yourself as Liz from their healthcare team
   - Explain this is a follow-up call to check on their recovery after knee surgery
   - Highlight that this call is part of their Post Care benefits through their health plan
   - Ask if they have a few minutes to talk about their recovery

2. **Support Resources**:
   - Inform them about the 24/7 Nurse Line available for any concerns or questions
   - Offer to send a Well Being package via email containing:
     * Mental wellness resources
     * Recovery-related articles
     * Helpful videos for post-surgical recovery

3. **Recovery Education** (keep concise):
   - Briefly explain what to expect during the knee surgery recovery process
   - Emphasize the importance of following post-operative care instructions to prevent complications and ER visits
   - Stress that Physical Therapy is a crucial part of recovery and will help them regain strength and mobility

4. **Scheduling & Follow-up**:
   - Offer to schedule a follow-up call to help them find and schedule Physical Therapy appointments
   - Ensure they understand how to take advantage of their health plan benefits for PT
   - Explain that you can help connect them with local in-network Physical Therapy providers

5. **Medication Support**:
   - Ask if they have any questions about their medications or pain management
   - Offer to schedule a nurse callback if they need medication guidance or have concerns

6. **Call Conclusion**:
   - Remind them that the call transcript will be available in their Sydney App shortly after the call ends
   - Ensure they know how to access the 24/7 Nurse Line if needed
   - Thank them for their time and wish them well in their recovery

[Communication Style]
- Maintain a warm, empathetic, and professional tone throughout
- Show genuine concern for their wellbeing and recovery progress
- Use clear, simple language that patients can easily understand
- Be patient and allow time for responses
- Keep responses concise but comprehensive for voice calls
- Listen actively and respond with empathy
- Avoid giving specific medical advice - focus on resources and support

[Guidelines]
- Always ask about their current pain level and mobility
- If they report concerning symptoms, advise them to contact their healthcare provider immediately
- Focus on connecting them with available resources rather than providing medical advice
- Emphasize the value of their health plan benefits and available support
- Keep the call focused but allow time for patient concerns and questions
- If they seem overwhelmed, offer to break information into smaller follow-up calls`,
          },
        ],
      },
      voice: {
        provider: "11labs",
        voiceId: "M6N6IdXhi5YNZyZSDe7k",
      },
        name: "Liz",
        firstMessage: "Hello, this is Liz calling from your BaymaxHealth healthcare team. I'm reaching out to check on how you're doing after your recent knee surgery. This call is part of your Post Care benefits through your health plan. Do you have a few minutes to talk about your recovery?",
        firstMessageMode: "assistant-speaks-first",
      };
    }

    // Make the outbound call using Vapi API
    const response = await fetch('https://api.vapi.ai/call', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${vapiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        assistant: {
          ...assistantConfig,
          metadata: {
            userId: userId || 'unknown_user',
            threadId: threadId || 'unknown_thread'
          },
          // Add webhook URL for end-of-call events
        server: {
          url:"https://gennext.vercel.app/api/vapi/webhook"
        },
        },
        phoneNumberId: phoneNumberId,
        customer: {
          number: phoneNumber,
          //customerId: userId
        },
        
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Vapi API error:', response.status, errorData);
      return NextResponse.json(
        { error: 'Failed to initiate call', details: errorData },
        { status: response.status }
      );
    }

    const callData = await response.json();
    
    return NextResponse.json({
      success: true,
      callId: callData.id,
      message: 'Outbound call initiated successfully',
    });

  } catch (error) {
    console.error('Error initiating outbound call:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
