'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@/contexts/ChatContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Eye } from 'lucide-react';
import { useSidebar } from '@/components/ui/sidebar';

const LoginPage = () => {
  const router = useRouter();
  const { createThread } = useChat();
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, [setOpen]);

  const handleLogin = () => {
    const threadId = createThread({
      name: "Personalized Condition Management Journey",
      initialMessage: "Begin Personalized Condition Management Scenario",
    });
    router.push('/journeys');
  };

  return (
    <div className="light-mode-override min-h-screen bg-white">
      {/* Top Navigation Bar */}
      <div className="flex justify-between items-center px-6 py-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50 px-4 py-2 rounded-full"
          >
            Find Care
          </Button>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search"
              className="pl-10 pr-4 py-2 w-64 border-gray-300 rounded-lg"
            />
          </div>
        </div>
        <div className="text-blue-600 hover:text-blue-700 cursor-pointer">
          Español
        </div>
      </div>

      {/* Main Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="flex justify-center space-x-8 py-3">
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Individual & Family</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Medicare</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Medicaid</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Employers</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Producers</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">Providers</div>
          <div className="text-gray-700 hover:text-blue-600 cursor-pointer font-medium">COVID-19 Info</div>
        </div>
      </div>

      {/* Login Form */}
      <div className="flex items-center justify-center min-h-[calc(100vh-140px)] bg-white">
        <div className="w-full max-w-sm mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">Log in to your member account</h1>
            <p className="text-gray-600">
              Not signed up?{' '}
              <span className="text-blue-600 hover:text-blue-700 cursor-pointer">Register now</span>
            </p>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address or Username
              </label>
              <Input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-gray-900"
                defaultValue="<EMAIL>"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <Input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10 dark:text-gray-900"
                  defaultValue="password123"
                  readOnly
                />
                <Eye className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
            </div>

            <div className="text-center">
              <span className="text-blue-600 hover:text-blue-700 cursor-pointer text-sm">
                Forgot Username or Password?
              </span>
            </div>

            <Button
              onClick={handleLogin}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium text-base"
            >
              Log In
            </Button>
          </div>

          {/* Footer Links */}
          <div className="mt-8 text-center text-sm text-gray-600 space-y-2">
            <p>
              Are you trying to shop for plans? You need to log in to{' '}
              <span className="text-blue-600 hover:text-blue-700 cursor-pointer">shop.baymaxhealth.com</span>
            </p>
            <p>
              Are you eligible for Medicare? Shop for Medicare plans today at{' '}
              <span className="text-blue-600 hover:text-blue-700 cursor-pointer">shop.baymaxhealth.com/medicare</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
