'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@/contexts/ChatContext';
import { ChevronLeft, Mail, Star, Archive, Trash2 } from 'lucide-react';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';

const CrohnsDiagnosisPage = () => {
  const router = useRouter();
  const { createThread } = useChat();
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, [setOpen]);

  const handleGetStarted = () => {
    router.push('/internal/login');
  };

  return (
    <div className="light-mode-override min-h-screen bg-gray-50">
      <div className="w-full max-w-6xl mx-auto bg-white shadow-lg overflow-hidden min-h-screen flex flex-col">
        {/* Email Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between z-10">
          <div className="flex items-center gap-4">
            <button onClick={() => router.back()} className="flex items-center text-blue-600 hover:text-blue-700">
              <ChevronLeft size={20} />
              <span className="text-sm ml-1">Back</span>
            </button>
            <div className="flex items-center gap-3">
              <Mail size={24} className="text-blue-600" />
              <h1 className="font-semibold text-xl text-gray-900">Inbox</h1>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">1 message</span>
            <div className="flex items-center gap-2">
              <Archive size={18} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
              <Trash2 size={18} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
            </div>
          </div>
        </header>

        {/* Email List Area */}
        <main className="flex-1 overflow-y-auto bg-white">
          {/* Email Item */}
          <div className="border-b border-gray-200 px-6 py-6 hover:bg-gray-50 transition-colors">
            <div className="flex items-start gap-4">
              <Avatar className="h-12 w-12 mt-1">
                <AvatarFallback className="bg-blue-600 text-white font-bold">A</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-semibold text-lg text-gray-900">BaymaxHealth</span>
                    <span className="text-sm text-gray-500"><EMAIL></span>
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-500">Today 9:41 AM</span>
                    <Star size={18} className="text-yellow-400 hover:text-yellow-500 cursor-pointer" />
                  </div>
                </div>
                <h2 className="font-semibold text-xl dark:text-gray-900! mb-4 leading-tight">
                  Support that’s built into your plan
                </h2>
                <div className="text-base text-gray-700 space-y-4 max-w-4xl">
                  <p>
                    Hi Taylor,
                  </p>
                  <p>
                    Your care journey is personal—and your support should be, too.
                  </p>
                  <p>
                    If you're managing a chronic condition, your health plan includes tools and resources to help you stay on top of your care. From coordinating appointments to managing medications, we're here to help make things feel more connected and less overwhelming. And it’s all included in your plan at no extra cost to you.
                  </p>
                  <p>
                    Here's some of the support your plan offers:
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>A dedicated nurse coordinator to help guide your care</li>
                    <li>Tools to stay on top of symptoms, treatments, and appointments</li>
                    <li>Simplified medication management with refills and reminders</li>
                  </ul>
                  <p>
                    You can get started anytime. When you're ready, we'll walk through it together.
                  </p>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <Button
                      onClick={handleGetStarted}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium text-base"
                    >
                      Get Started
                    </Button>
                  </div>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                      With care,
                    </p>
                    <p className="text-sm text-gray-500">
                      BaymaxHealth
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Status Bar */}
        <footer className="bg-gray-50 border-t border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">1 message</span>
              <span className="text-sm text-gray-600">•</span>
              <span className="text-sm text-gray-600">1 unread</span>
            </div>
            <div className="flex items-center gap-4">
              <button className="text-sm text-blue-600 hover:text-blue-700">
                Mark all as read
              </button>
              <button className="text-sm text-gray-600 hover:text-gray-700">
                Settings
              </button>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default CrohnsDiagnosisPage;
