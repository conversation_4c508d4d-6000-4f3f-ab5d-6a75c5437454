'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@/contexts/ChatContext';
import { ChevronLeft, Mail, Star, Archive, Trash2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';

const PriorAuthSteeragePage = () => {
  const router = useRouter();
  const { createThread } = useChat();
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, [setOpen]);

  const handleViewCostSavingOptions = () => {
    const threadId = createThread({
      name: "Prior Authorization Steerage",
      initialMessage: "Begin Prior Authorization Steerage",
    });
    router.push('/journeys');
  };

  return (
    <div className="light-mode-override min-h-screen bg-gray-50">
      <div className="w-full max-w-6xl mx-auto bg-white shadow-lg overflow-hidden min-h-screen flex flex-col">
        {/* Email Header */}<span className="dark:text-black"> Simulated Email Inbox</span>
        <header className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between z-10">
          <div className="flex items-center gap-4">
            <button onClick={() => router.back()} className="flex items-center text-blue-600 hover:text-blue-700">
              <ChevronLeft size={20} />
              <span className="text-sm ml-1">Back</span>
            </button>
            <div className="flex items-center gap-3">
              <Mail size={24} className="text-blue-600" />
              <h1 className="font-semibold text-xl text-gray-900">Inbox</h1>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">3 messages</span>
            <div className="flex items-center gap-2">
              <Archive size={18} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
              <Trash2 size={18} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
            </div>
          </div>
        </header>

        {/* Email List Area */}
        <main className="flex-1 overflow-y-auto bg-white">
          {/* Email Item */}
          <div className="border-b border-gray-200 px-6 py-6 hover:bg-gray-50 transition-colors">
            <div className="flex items-start gap-4">
              <Avatar className="h-12 w-12 mt-1">
                <AvatarFallback className="bg-blue-600 text-white font-bold">A</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-semibold text-lg text-gray-900">BaymaxHealth</span>
                    <span className="text-sm text-gray-500"><EMAIL></span>
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-500">Today 2:34 PM</span>
                    <Star size={18} className="text-gray-400 hover:text-yellow-500 cursor-pointer" />
                  </div>
                </div>
                <h2 className="font-semibold text-xl dark:text-gray-900! mb-4 leading-tight">
                  Your X-ray Imaging Prior Authorization has been Approved
                </h2>
                <div className="text-base text-gray-700 space-y-4 max-w-4xl">
                  <p>
                    Great news! Your prior authorization request for X-ray imaging has been approved and is ready for scheduling.
                  </p>
                  <p>
                    As part of your comprehensive health plan, we want to help you maximize your benefits while minimizing your out-of-pocket costs. We've identified several cost-effective imaging centers in your area that could provide significant savings.
                  </p>
                  <p>
                    These alternative providers offer the same high-quality imaging services at reduced costs, helping you save money while receiving excellent care.
                  </p>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <Button
                      onClick={handleViewCostSavingOptions}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium text-base"
                    >
                      View Cost-Saving Options
                    </Button>
                  </div>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                      Questions? Contact Member Services at 1-800-BAYMAX-1 or visit baymaxhealth.com
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional placeholder emails for context */}
          <div className="border-b border-gray-200 px-6 py-4 opacity-60">
            <div className="flex items-start gap-4">
              <Avatar className="h-12 w-12 mt-1">
                <AvatarFallback className="bg-green-600 text-white font-bold">WB</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-semibold text-gray-700">Wellness Benefits</span>
                    <span className="text-sm text-gray-500"><EMAIL></span>
                  </div>
                  <span className="text-sm text-gray-500">Yesterday</span>
                </div>
                <h3 className="font-medium text-lg text-gray-700 mb-2 leading-tight">
                  Your Monthly Wellness Report is Ready
                </h3>
                <p className="text-base text-gray-500 line-clamp-2">
                  See your progress and discover new ways to improve your health...
                </p>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 px-6 py-4 opacity-60">
            <div className="flex items-start gap-4">
              <Avatar className="h-12 w-12 mt-1">
                <AvatarFallback className="bg-purple-600 text-white font-bold">RX</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-semibold text-gray-700">Pharmacy Benefits</span>
                    <span className="text-sm text-gray-500"><EMAIL></span>
                  </div>
                  <span className="text-sm text-gray-500">2 days ago</span>
                </div>
                <h3 className="font-medium text-lg text-gray-700 mb-2 leading-tight">
                  Prescription Refill Reminder
                </h3>
                <p className="text-base text-gray-500 line-clamp-2">
                  Your prescription for Mesalamine is ready for refill...
                </p>
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Status Bar */}
        <footer className="bg-gray-50 border-t border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">3 messages</span>
              <span className="text-sm text-gray-600">•</span>
              <span className="text-sm text-gray-600">1 unread</span>
            </div>
            <div className="flex items-center gap-4">
              <button className="text-sm text-blue-600 hover:text-blue-700">
                Mark all as read
              </button>
              <button className="text-sm text-gray-600 hover:text-gray-700">
                Settings
              </button>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default PriorAuthSteeragePage;
